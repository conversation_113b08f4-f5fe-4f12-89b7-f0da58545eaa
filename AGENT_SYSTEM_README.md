# Suna Agent System Integration

**Status**: ✅ Complete - Ready for Testing  
**Date**: July 22, 2025  
**Integration**: AA-MCP-MVP + Suna Agent Framework

## 🚀 Quick Start

### 1. Start All Services
```bash
# Single command to start everything
./start-all.sh
```

This will automatically:
- Start Redis and RabbitMQ Docker containers
- Launch FastAPI backend on port 8000
- Start Dramatiq background worker
- Launch Next.js frontend on port 5555
- Perform health checks for all services

### 2. Access Agent Interface
```bash
# Open in browser
http://localhost:5555/agent/agents
```

### 3. Test Agent Functionality
1. **Create Agent**: Click "Create New Agent" 
2. **Start Chat**: Click on an agent to open chat interface
3. **Send Message**: Type a message and press Enter
4. **Watch Processing**: See real-time status updates
5. **Receive Response**: Agent responses appear automatically

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Next.js       │    │   FastAPI       │    │   Redis         │
│   Frontend      │───▶│   Backend       │───▶│   (Docker)      │
│   Port: 5555    │    │   Port: 8000    │    │   Port: 6379    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
        │                       │                       
        │                       ▼                       
        │               ┌─────────────────┐    ┌─────────────────┐
        │               │   Dramatiq      │    │   RabbitMQ      │
        └──────────────▶│   Worker        │───▶│   (Docker)      │
                        │   Background    │    │   Port: 5672    │
                        └─────────────────┘    └─────────────────┘
```

## 📁 Key Files Created/Modified

### New Components
- `src/components/agent-chat-bot.tsx` - Agent-specific chat interface
- `src/hooks/use-agent-polling.ts` - Real-time response polling
- `src/app/api/agent/chat/route.ts` - Backend API integration

### Updated Files
- `src/app/agent/agents/[threadId]/redirect-page.tsx` - Uses AgentChatBot
- `.env.local` - Backend URL configuration
- `agent_backend/.env` - Service connection settings

### Scripts & Documentation
- `start-all.sh` - Unified startup script
- `test-agent-system.sh` - Integration test suite
- `docs/FEATURES_PLANNING/AGENT-[Suna]_planning/` - Complete documentation

## 🔧 Manual Setup (Alternative)

If `start-all.sh` doesn't work, start services manually:

### 1. Infrastructure Services
```bash
# Start Redis
docker run -d --name redis_aa -p 6379:6379 redis:alpine

# Start RabbitMQ  
docker run -d --name rabbitmq_aa -p 5672:5672 -p 15672:15672 rabbitmq:3-management
```

### 2. Backend Services
```bash
# Terminal 1: FastAPI Backend
cd agent_backend
uv run api.py

# Terminal 2: Dramatiq Worker
cd agent_backend  
uv run dramatiq run_agent_background
```

### 3. Frontend Service
```bash
# Terminal 3: Next.js Frontend
pnpm dev
```

## 🧪 Testing

### Run Integration Tests
```bash
# Test system readiness
./test-agent-system.sh
```

### Manual Testing Checklist
- [ ] All services start without errors
- [ ] Frontend loads at http://localhost:5555
- [ ] Backend API docs accessible at http://localhost:8000/docs
- [ ] Agent page loads without errors
- [ ] Can create new agents
- [ ] Agent chat interface appears
- [ ] Messages send successfully
- [ ] Backend receives messages (check logs)
- [ ] Polling system works (see status updates)

## 🌐 Production Deployment

See `docs/FEATURES_PLANNING/AGENT-[Suna]_planning/PRODUCTION_DEPLOYMENT_PLAN.md` for:
- Railway backend deployment
- Vercel frontend deployment  
- Upstash Redis setup
- CloudAMQP RabbitMQ setup
- Environment configuration
- Cost estimation (~$5/month)

## 🐛 Troubleshooting

### Common Issues

**Docker Containers Won't Start**
```bash
# Check Docker is running
docker info

# Remove existing containers
docker stop redis_aa rabbitmq_aa
docker rm redis_aa rabbitmq_aa
```

**Backend Connection Errors**
```bash
# Check environment files
cat agent_backend/.env | grep HOST
cat .env.local | grep BACKEND_URL

# Verify ports are available  
lsof -i :8000 -i :6379 -i :5672
```

**Frontend Agent Page Errors**
```bash
# Check browser console for errors
# Verify backend is running:
curl http://localhost:8000/health
```

**Agent Messages Not Processing**
```bash
# Check backend logs for errors
# Verify RabbitMQ connection:
curl http://localhost:15672 (guest/guest)

# Check Redis connection:
redis-cli -h localhost -p 6379 ping
```

### Service URLs
- **Frontend**: http://localhost:5555
- **Backend API**: http://localhost:8000/docs
- **RabbitMQ Management**: http://localhost:15672 (guest/guest)
- **Agent Chat**: http://localhost:5555/agent/agents

## 📞 Support

**Documentation**: `docs/FEATURES_PLANNING/AGENT-[Suna]_planning/`
**Logs**: Check individual terminal outputs for service-specific errors
**Health Checks**: Built into startup script and API endpoints

---

**Ready to test! 🎉**

The agent system is fully integrated and ready for local testing. Follow the Quick Start guide above to begin.