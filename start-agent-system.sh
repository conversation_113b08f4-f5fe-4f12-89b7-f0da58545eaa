#!/bin/bash

# Agent System Startup Script
# This script starts both the frontend (Next.js) and backend (FastAPI) components

set -e  # Exit on any error

echo "🚀 Starting Agent System..."
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from the project root directory."
    exit 1
fi

# Stop any existing processes
print_status "Stopping any existing processes..."
pkill -f "next dev" || true
pkill -f "python api.py" || true
pkill -f "uvicorn" || true
pkill -f "dramatiq" || true
sleep 2

# Check frontend dependencies
print_status "Checking frontend dependencies..."
if [ ! -d "node_modules" ]; then
    print_status "Installing frontend dependencies..."
    pnpm install
fi
print_success "Frontend dependencies ready"

# Check backend dependencies
print_status "Checking backend dependencies..."
if [ ! -d "agent_backend" ]; then
    print_error "agent_backend directory not found!"
    exit 1
fi

cd agent_backend

# Check for Python virtual environment
if [ ! -d ".venv" ]; then
    print_status "Creating Python virtual environment..."
    uv venv .venv
fi

# Activate virtual environment and install dependencies
print_status "Installing backend dependencies..."
source .venv/bin/activate
uv pip install -r ../agent_backend/pyproject.toml 2>/dev/null || uv sync

# Check environment file
if [ ! -f ".env" ]; then
    print_warning "No .env file found in agent_backend/"
    print_status "Creating .env from .env.example if available..."
    if [ -f ".env.example" ]; then
        cp .env.example .env
        print_warning "Please edit agent_backend/.env with your configuration"
    else
        print_error "No .env.example found. Please create agent_backend/.env manually"
        exit 1
    fi
fi

cd ..
print_success "Backend dependencies ready"

# Start the services
print_status "Starting backend (FastAPI)..."
cd agent_backend
source .venv/bin/activate

# Start backend process in background
nohup python api.py > ../backend.log 2>&1 &
BACKEND_PID=$!
echo $BACKEND_PID > ../backend.pid

cd ..

# Start Dramatiq worker in background
print_status "Starting Dramatiq worker..."
cd agent_backend
nohup python -m dramatiq run_agent_background > ../worker.log 2>&1 &
WORKER_PID=$!
echo $WORKER_PID > ../worker.pid
cd ..

# Wait for backend to start
print_status "Waiting for backend to start..."
sleep 5

# Check if backend is running
if curl -f http://localhost:8000/api/health >/dev/null 2>&1; then
    print_success "Backend is running on http://localhost:8000"
else
    print_warning "Backend may not be ready yet. Check backend.log for details."
fi

# Start frontend
print_status "Starting frontend (Next.js)..."
pnpm dev &
FRONTEND_PID=$!
echo $FRONTEND_PID > frontend.pid

# Wait for frontend to start
print_status "Waiting for frontend to start..."
sleep 10

# Check if frontend is running
if curl -f http://localhost:5555 >/dev/null 2>&1; then
    print_success "Frontend is running on http://localhost:5555"
else
    print_warning "Frontend may not be ready yet. It might still be starting up."
fi

echo ""
print_success "🎉 Agent System Started Successfully!"
echo "=================================="
echo "Frontend: http://localhost:5555"
echo "Backend API: http://localhost:8000"
echo "Backend Health: http://localhost:8000/api/health"
echo ""
echo "Logs:"
echo "  Backend: backend.log"
echo "  Worker: worker.log"
echo "  Frontend: Check terminal output"
echo ""
echo "To stop all services:"
echo "  ./stop-agent-system.sh"
echo ""
echo "Press Ctrl+C to stop this script (services will continue running)"

# Keep script running to show logs
wait