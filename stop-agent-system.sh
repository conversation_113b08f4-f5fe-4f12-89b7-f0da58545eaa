#!/bin/bash

# Agent System Stop Script
# This script stops both the frontend and backend services

echo "🛑 Stopping Agent System..."
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# Stop processes by PID files
if [ -f "backend.pid" ]; then
    BACKEND_PID=$(cat backend.pid)
    print_status "Stopping backend process (PID: $BACKEND_PID)..."
    kill $BACKEND_PID 2>/dev/null || true
    rm backend.pid
fi

if [ -f "worker.pid" ]; then
    WORKER_PID=$(cat worker.pid)
    print_status "Stopping worker process (PID: $WORKER_PID)..."
    kill $WORKER_PID 2>/dev/null || true
    rm worker.pid
fi

if [ -f "frontend.pid" ]; then
    FRONTEND_PID=$(cat frontend.pid)
    print_status "Stopping frontend process (PID: $FRONTEND_PID)..."
    kill $FRONTEND_PID 2>/dev/null || true
    rm frontend.pid
fi

# Kill any remaining processes
print_status "Stopping any remaining processes..."
pkill -f "next dev" || true
pkill -f "python api.py" || true
pkill -f "uvicorn" || true
pkill -f "dramatiq" || true

# Wait a moment
sleep 2

print_success "🎉 Agent System Stopped Successfully!"
echo "All services have been stopped."